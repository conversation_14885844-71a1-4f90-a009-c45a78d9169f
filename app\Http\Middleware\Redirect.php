<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class Redirect
{
    public function handle(Request $request, Closure $next, $guard = null)
    {
        if (Auth::guard($guard)->check()) {
            $user = Auth::user();
            // Only redirect if user is admin
            if ($user && $user->role === 'admin') {
                return redirect()->route('dashboard')->with('info', 'You are already logged in.');
            }
        }

        return $next($request);
    }
}

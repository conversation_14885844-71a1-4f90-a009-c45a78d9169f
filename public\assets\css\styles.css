/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.form-section:last-child {
    border-bottom: 0px !important;
}

:root {
    --primary: #1e3a8a;
    --secondary: #3b82f6;
    --light-bg: #f8fafc;
    --dark-text: #333;
    --muted-text: #666;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
    color: #1e3a8a;
    font-weight: 600;
    margin-bottom: 1rem;
}

h1 {
    font-size: 2.5rem;
    line-height: 1.2;
}

h2 {
    font-size: 2rem;
    line-height: 1.3;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1rem;
    color: #666;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    color: #fff;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.6);
}

.btn-secondary {
    background-color: transparent;
    color: #1e3a8a;
    border: 2px solid #3b82f6;
    box-shadow: 0 2px 10px rgba(59, 130, 246, 0.2);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

/* Navigation */
.navbar {
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    border-radius: 0px 0px 20px 20px;
    padding: 10px 0px;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo {
    height: 70px;
    width: auto;
}

.company-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e3a8a;
}

.nav-menu {
    display: flex;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #1e3a8a;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: #1e3a8a;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 146px 0 70px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    position: relative;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-content {
    max-width: 500px;
}

.hero-title {
    font-size: 3rem;
    color: #1e3a8a;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.hero-image {

    border-radius: 50%;
}

.hero-image img {
    right: 0;
    bottom: 0;
    position: absolute;
    width: 28%;
}

.local-badge {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
    transition: all 0.3s ease;
}

.local-badge:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(59, 130, 246, 0.3);
}

/* Service Section */
.service-section {
    padding: 80px 0;
    background-color: #fff;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    color: #1e3a8a;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-bottom: 80px;
}

.service-card {
    text-align: center;
    padding: 40px 20px;
    border-radius: 15px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.1);
}

.service-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 50px rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
}

.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 2rem;
    box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1);
    box-shadow: 0 15px 30px rgba(59, 130, 246, 0.4);
}

.service-card h3 {
    color: #1e3a8a;
    margin-bottom: 15px;
}

.service-card p {
    color: #666;
    line-height: 1.6;
}

/* Ads Section */
.ads-section {
    margin-top: 60px;
    text-align: center;
}

.ads-section h3 {
    color: #1e3a8a;
    margin-bottom: 40px;
    font-size: 2rem;
}

.ads-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    max-width: 900px;
    margin: 0 auto;
}

.ad-item {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.2);
    transition: all 0.3s ease;
}

.ad-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(59, 130, 246, 0.3);
}

.ad-image {
    width: 100%;
    height: auto;
    display: block;
}

.main-ad {
    min-height: 300px;
    object-fit: cover;
}

/* Booking Section */
.booking-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.booking-form {
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
    border: 1px solid rgba(59, 130, 246, 0.1);
    position: relative;
}

img.like-image {
    width: 100px;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
    margin: 0px auto 21px auto;
    display: flex;
}

.form-section {
    margin-bottom: 40px;
    border-bottom: 1px solid #e9ecef;
}


.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h3 {
    color: #1e3a8a;
    margin-bottom: 25px;
    font-size: 1.3rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #1e3a8a;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background-color: #fff;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.radio-group {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.radio-label {
    display: flex !important;
    align-items: center;
    cursor: pointer;
    font-weight: normal;
}

.radio-label input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid #ccc;
    border-radius: 50%;
    margin-right: 10px;
    position: relative;
    transition: border-color 0.3s ease;
    display: flex;
}

.radio-label input[type="radio"]:checked+.radio-custom {
    border-color: #3b82f6;
}

.radio-label input[type="radio"]:checked+.radio-custom::after {
    content: '';
    width: 10px;
    height: 10px;
    background-color: #3b82f6;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.form-submit {
    text-align: center;
    margin-top: 40px;
}

.form-note {
    margin-top: 15px;
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
}

/* Testimonials Section */
.testimonials-section {
    padding: 80px 0;
    background-color: #fff;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.testimonial-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.2);
}

.testimonial-content {
    margin-bottom: 25px;
}

.testimonial-content p {
    color: #475569;
    font-style: italic;
    line-height: 1.6;
    margin: 0;
    font-size: 1.05rem;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.author-info h4 {
    color: #1e3a8a;
    margin: 0 0 5px 0;
    font-size: 1.1rem;
}

.author-info span {
    color: #666;
    font-size: 0.9rem;
}

/* Contact Section */
.contact-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.contact-card {
    text-align: center;
    padding: 30px 20px;
}

.contact-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.5rem;
    box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
}

.contact-card h3 {
    color: #1e3a8a;
    margin-bottom: 10px;
}

.contact-card p {
    color: #666;
    font-size: 1.1rem;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    color: #fff;
    padding: 00px 0 20px;
    border-radius: 30px 30px 0px 0px;
}

.footer-content {
    display: flex;
    gap: 10px;
    margin-bottom: 40px;
}

.footer-section h4 {
    color: #fff;
    margin-bottom: 20px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.footer-logo-img {
    height: 30px;
    width: auto;
    filter: brightness(0) invert(1);
}

.footer-company-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #fff;
}

.footer-section p {
    color: #ccc;
    line-height: 1.6;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #fff;
}

.footer-contact p {
    color: #ccc;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-contact i {
    width: 16px;
}

.footer-bottom {
    border-top: 1px solid #333;
    padding-top: 20px;
    text-align: center;
}

.footer-bottom p {
    color: #ccc;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: #fff;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 20px 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu a {
        padding: 15px;
        display: block;
        border-bottom: 1px solid #eee;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-title {
        font-size: 2.2rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .ads-container {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .contact-grid {
        grid-template-columns: 1fr;
    }

    .service-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.8rem;
    }

    .booking-form {
        padding: 20px;
        margin: 0 10px;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 250px;
    }

    /* Multi-step form mobile styles */
    .progress-steps {
        flex-wrap: wrap;
        gap: 10px;
    }

    .progress-step {
        flex: 0 1 calc(50% - 5px);
        margin-bottom: 15px;
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .step-label {
        font-size: 0.8rem;
    }

    .form-navigation {
        flex-direction: column;
        gap: 15px;
    }

    .form-navigation .btn {
        width: 100%;
        min-width: auto;
        display: flex !important;
        align-items: center;
        gap: 10px;
    }

    .d-flex {
        display: flex !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    .gap-5 {
        gap: 10px !important;
    }

    .form-navigation #nextBtn,
    .form-navigation #submitBtn {
        margin-left: 0;
        order: 1;
    }

    .form-navigation #prevBtn {
        order: 2;
    }
}

/* Multi-step Form Styles */
.progress-container {
    margin-bottom: 40px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background-color: #e9ecef;
    border-radius: 3px;
    margin-bottom: 20px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1e3a8a);
    border-radius: 3px;
    width: 25%;
    transition: width 0.3s ease;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
    transition: color 0.3s ease;
}

/* Inactive steps - light background */
.progress-step .step-number {
    background-color: #f3f4f6;
    color: #9ca3af;
}

.progress-step .step-label {
    color: #9ca3af;
}

/* Active step - blue background */
.progress-step.active .step-number {
    background-color: #3b82f6;
    color: white;
}

.progress-step.active .step-label {
    color: #3b82f6;
}

/* Completed steps - green background with white checkmark */
.progress-step.completed .step-number {
    background-color: #10b981;
    color: white;
    position: relative;
}

.progress-step.completed .step-number::before {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    font-weight: bold;
    color: white;
}

.progress-step.completed .step-number {
    font-size: 0;
    /* Hide the number */
}

.progress-step.completed .step-label {
    color: #10b981;
}

/* Review Section Styles */
.review-section {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 24px;
    margin-top: 20px;
}

.review-section h4 {
    color: #1e293b;
    margin-bottom: 24px;
    font-size: 1.1rem;
    font-weight: 600;
}

.review-group {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e2e8f0;
}

.review-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.review-group h5 {
    color: #3b82f6;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 12px;
}

.review-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
    padding: 8px 0;
}

.review-label {
    font-weight: 500;
    color: #64748b;
    min-width: 140px;
    flex-shrink: 0;
}

.review-value {
    color: #1e293b;
    font-weight: 500;
    text-align: right;
    flex: 1;
    margin-left: 16px;
}

@media (max-width: 768px) {
    .review-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .review-value {
        text-align: left;
        margin-left: 0;
        margin-top: 4px;
    }
}

.form-step {
    display: none;
    animation: fadeIn 0.3s ease;
}

.form-step.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.form-navigation .btn {
    min-width: 120px;
}

.form-navigation #nextBtn,
.form-navigation #submitBtn {
    margin-left: auto;
}

/* Form Validation Styles */
.form-group input.invalid,
.form-group select.invalid,
.form-group textarea.invalid {
    border-color: #dc3545;
}

.error-message {
    color: #dc3545;
    font-size: 0.9rem;
    margin-top: 5px;
    display: none;
}

.error-message.show {
    display: block;
}

/* Success Message */
.success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #c3e6cb;
    display: none;
}

.success-message.show {
    display: block;
}

/* Loading State */
.btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
    position: relative;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Section Spacing */
section {
    scroll-margin-top: 70px;
}

/* Page Header Styles */
.page-header {
    padding: 120px 0 60px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    text-align: center;
}

.page-header h1 {
    font-size: 2.5rem;
    color: #1e3a8a;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* How It Works Section */
.how-it-works-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.steps-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 900px;
    margin: 0 auto;
    gap: 20px;
}

.step-item {
    flex: 1;
    text-align: center;
    max-width: 250px;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 20px;
    box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
    transition: all 0.3s ease;
}

.step-item:hover .step-number {
    transform: scale(1.1);
    box-shadow: 0 12px 24px rgba(59, 130, 246, 0.4);
}

.step-content h3 {
    color: #1e3a8a;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.step-content p {
    color: #666;
    line-height: 1.6;
    font-size: 0.95rem;
}

.step-arrow {
    color: #666;
    font-size: 1.5rem;
    margin: 0 10px;
}

/* About Page Styles */
.about-content {
    padding: 80px 0;
    background-color: #fff;
}

.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: center;
}

.content-text h2 {
    color: #1e3a8a;
    margin-bottom: 20px;
}

.content-text h3 {
    color: #1e3a8a;
    margin-top: 30px;
    margin-bottom: 15px;
}

.content-text p {
    color: #666;
    line-height: 1.7;
    margin-bottom: 20px;
}

.about-image {
    width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.services-overview {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-top: 50px;
}

.service-item {
    text-align: center;
    padding: 30px 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
}

.service-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.2);
}

.values-section {
    padding: 80px 0;
    background-color: #fff;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.value-card {
    padding: 30px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.08);
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(59, 130, 246, 0.12);
    border-color: rgba(59, 130, 246, 0.2);
}

.value-card h3 {
    color: #1e3a8a;
    margin-bottom: 15px;
}

.value-card p {
    color: #666;
    line-height: 1.6;
}

/* Contact Page Styles */
.contact-info-section {
    padding: 80px 0;
    background-color: #fff;
}

.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.contact-card-large {
    text-align: center;
    padding: 40px 30px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 15px;
    box-shadow: 0 15px 30px rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
}

.contact-card-large:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.2);
}

.contact-card-large .contact-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 25px;
    background-color: #000;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 2rem;
}

.contact-card-large h3 {
    color: #1e3a8a;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.contact-detail {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 10px;
}

.contact-hours {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
}

.contact-form-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.form-container {
    max-width: 700px;
    margin: 0 auto;
}

.form-container h2 {
    text-align: center;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.form-container>p {
    text-align: center;
    color: #666;
    margin-bottom: 40px;
    font-size: 1.1rem;
}

.contact-form {
    background-color: #fff;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.faq-section {
    padding: 80px 0;
    background-color: #fff;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.faq-question.active h3 {
    color: #fff;
}

.faq-item {
    /* padding: 25px; */
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 15px;
    border: 1px solid rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.08);
}

.faq-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.12);
    border-color: rgba(59, 130, 246, 0.2);
}

.faq-item h3 {
    color: #1e3a8a;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.faq-item p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* Legal Page Styles */
.legal-content {
    padding: 80px 0;
    background-color: #fff;
}

.legal-document {
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.7;
}

.legal-document h2 {
    color: #1e3a8a;
    margin-top: 40px;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.legal-document p {
    color: #333;
    margin-bottom: 15px;
}

.legal-document ul {
    margin: 15px 0;
    padding-left: 25px;
}

.legal-document li {
    color: #333;
    margin-bottom: 8px;
}

.legal-document strong {
    color: #1e3a8a;
}

/* Active Navigation Link */
.nav-link.active {
    color: #1e3a8a;
    font-weight: 600;
}

/* Enhanced Footer */
.footer-content {
    display: flex;
    justify-content: center;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

/* Responsive Design Updates */
@media (max-width: 768px) {
    .steps-container {
        flex-direction: column;
        gap: 30px;
    }

    .step-arrow {
        transform: rotate(90deg);
        margin: 15px 0;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .contact-info-grid {
        grid-template-columns: 1fr;
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }

    .values-grid {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .contact-form {
        padding: 25px;
        margin: 0 10px;
    }
}

@media (max-width: 480px) {
    .step-number {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .contact-card-large {
        padding: 25px 20px;
    }

    .contact-card-large .contact-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* Pricing Section Styles */
.pricing-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    border: 2px solid rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.1);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
}

.pricing-card.featured {
    transform: scale(1.05);
    border-color: #1e3a8a;
    box-shadow: 0 20px 40px rgba(30, 58, 138, 0.2);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
    box-shadow: 0 30px 60px rgba(30, 58, 138, 0.3);
}

.popular-badge {
    position: absolute;
    top: 20px;
    right: -45px;
    background: #1e3a8a;
    color: white;
    padding: 8px 40px;
    font-size: 0.9rem;
    font-weight: 600;
    transform: rotate(45deg);
    box-shadow: 0 4px 8px rgba(30, 58, 138, 0.3);
}

.pricing-header h3 {
    color: #1e3a8a;
    font-size: 1.5rem;
    font-weight: 600;
}



.currency {
    font-size: 1.5rem;
    color: #1e3a8a;
    vertical-align: top;
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: #1e3a8a;
}

.period {
    font-size: 1.2rem;
    color: #666;
    margin-left: 5px;
}

.savings {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 2px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 10px;
    position: absolute;
    left: 15px;
    top: 15px;
}

.pricing-features {
    margin: 0px 0 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.pricing-features ul {
    list-style: none;
    padding: 0;
}

.pricing-features li {
    padding: 5px 0;
    color: #475569;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    text-align: left;
}

.pricing-features li i {
    color: #10b981;
    margin-right: 12px;
    font-size: 1rem;
    width: 16px;
}

.pricing-footer {
    margin-top: 14px;
}

.btn-outline {
    background: transparent;
    color: #1e3a8a;
    border: 2px solid #1e3a8a;
    padding: 12px 30px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-outline:hover {
    background: #1e3a8a;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(30, 58, 138, 0.3);
}

/* Mobile Responsive Pricing Styles */
@media (max-width: 768px) {
    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 30px;
        padding: 0 20px;
    }

    .hero-image {
        z-index: 1;
    }

    .hero-content {
        z-index: 2;
    }

    .hero-image img {
        width: 67%;
    }

    .hero-buttons a.btn.btn-secondary {
        background: #e5f4fed4;
    }

    .hero-buttons .btn-primary {
        background: linear-gradient(135deg, #3b82f6e6 0%, #1e40aff0 100%);
    }

    .pricing-card.featured {
        transform: none;
        order: -1;
    }

    .pricing-card.featured:hover {
        transform: translateY(-10px);
    }

    .pricing-card {
        padding: 30px 25px;
    }

    .popular-badge {
        top: 15px;
        right: -25px;
        padding: 6px 35px;
        font-size: 0.8rem;
    }

    .amount {
        font-size: 2.5rem;
    }

    .pricing-features li {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .pricing-section {
        padding: 60px 0;
    }

    .pricing-card {
        padding: 25px 20px;
        margin: 0 10px;
    }

    .amount {
        font-size: 2rem;
    }

    .pricing-header h3 {
        font-size: 1.3rem;
        margin-top: 15px;
    }

    .popular-badge {
        top: 22px;
        right: -30px;
        padding: 5px 30px;
        font-size: 0.75rem;
    }

    .page-header {
        padding: 120px 0 30px;
    }

    .footer-content {
        gap: 15px;
        margin-bottom: 0;
    }
}

/* FAQ Section */
.faq-section {
    padding: 4rem 0;
    background: #f8f9fa;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: white;
    margin-bottom: 1rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.faq-question {
    padding: 1.5rem 2rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    transition: all 0.3s ease;
    border-bottom: 1px solid transparent;
}

.faq-question:hover {
    background: #f8f9fa;
}

.faq-question.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.faq-question h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.faq-icon {
    font-size: 1.5rem;
    font-weight: bold;
    transition: all 0.3s ease;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.1);
    padding-bottom: 5px;
}

.faq-question.active .faq-icon {
    transform: rotate(45deg);
    background: rgba(255, 255, 255, 0.2);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: #f8f9fa;
}

.faq-answer.active {
    max-height: 200px;
    padding: 1.5rem 2rem;
}

.faq-answer p {
    margin: 0;
    line-height: 1.6;
    color: #555;
}

/* FAQ Mobile Responsive */
@media (max-width: 768px) {
    .faq-question {
        padding: 1rem 1.5rem;
    }

    .faq-question h3 {
        font-size: 1rem;
    }

    .faq-answer.active {
        padding: 1rem 1.5rem;
    }
}

.text-center {
    text-align: center;
}

/* make all footer links sit side‑by‑side */
.footer-section .footer-links {
    display: flex;
    flex-wrap: wrap;
    /* allow wrapping on very small screens */
    gap: 15px 30px;
    /* horizontal and vertical gutters */
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section .footer-links li {
    margin: 0;
    /* remove default bottom margin */
}

/* ensure link text doesn’t break mid‑word */
.footer-section .footer-links a {
    white-space: nowrap;
}

/* Progress Indicator */
.progress-container {
    max-width: 600px;
    margin: 0 auto 40px;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background-color: #e5e7eb;
    border-radius: 2px;
    margin-bottom: 20px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    width: 33.33%;
    transition: width 0.3s ease;
    border-radius: 2px;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    flex: 1;
    text-align: center;
}

.step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e5e7eb;
    color: #9ca3af;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.3s ease;
}

.progress-step.active .step-circle {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    color: white;
}

.progress-step.completed .step-circle {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    position: relative;
}

.progress-step.completed .step-circle::before {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    font-weight: bold;
    color: white;
}

.progress-step.completed .step-number {
    opacity: 0;
}

.progress-step span {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.progress-step .step-number {
    color: #000;
}

.progress-step.active span {
    color: #1e40af;
    font-weight: 600;
}



/* Step Badge */
.step-badge {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-right: 10px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Navigation */
.form-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
}

.form-navigation .btn {
    min-width: 120px;
}

.form-navigation .btn:only-child {
    margin-left: auto;
}

@media (max-width: 768px) {
    .progress-steps {
        gap: 10px;
    }

    .progress-container {

        margin-bottom: 0;
    }

    .progress-step span {
        font-size: 0.8rem;
        display: none;
    }

    .step-circle {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .form-navigation {
        flex-direction: column;
        gap: 15px;
    }

    .form-navigation .btn {
        width: 100%;
    }
}

/* Authentication Styles */
.auth-section {
    min-height: calc(100vh - 80px);
    padding: 40px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    margin-top: 70px;
}

.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 600px;
    margin: 0 auto;
}

.auth-card {
    background: #fff;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    width: 100%;
    max-width: 500px;
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h2 {
    color: #1e3a8a;
    font-size: 2rem;
    margin-bottom: 10px;
}

.auth-header p {
    color: #666;
    font-size: 1rem;
}

.auth-form {
    display: flex;
    flex-direction: column;
    /* gap: 20px; */
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 15px;
    color: #666;
    z-index: 2;
}

.input-group input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fff;
}

.input-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.password-toggle {
    position: absolute;
    right: 15px;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 5px;
    z-index: 2;
}

.password-toggle:hover {
    color: #3b82f6;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-size: 0.9rem;
    color: #666;
}

.checkbox-custom {
    width: 18px;
    height: 18px;
    border: 2px solid #e2e8f0;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-label input[type="checkbox"]:checked+.checkbox-custom {
    background: #3b82f6;
    border-color: #3b82f6;
}

.checkbox-label input[type="checkbox"]:checked+.checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 12px;
    font-weight: bold;
}

.forgot-link {
    color: #3b82f6;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-link:hover {
    color: #1e40af;
}

.auth-submit {
    width: 100%;
    margin: 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.auth-divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

/* .auth-divider::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: #e2e8f0;
    } */

.auth-divider span {
    background: #fff;
    padding: 0 20px;
    color: #666;
    font-size: 0.9rem;
}

.auth-image {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    height: 500px;
}

.auth-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.auth-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.8) 0%, rgba(59, 130, 246, 0.8) 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: #fff;
    padding: 40px;
}

.auth-overlay h3 {
    color: #fff;
    font-size: 1.8rem;
    margin-bottom: 15px;
}

.auth-overlay p {
    color: #e2e8f0;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Password Requirements */
.password-requirements {
    margin-top: 5px;
}

.password-requirements small {
    color: #666;
    font-size: 0.8rem;
}

/* OTP Input Styles */
.otp-input-group {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin: 20px 0;
}

.otp-input {
    width: 50px;
    height: 50px;
    text-align: center;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1.2rem;
    font-weight: bold;
    transition: all 0.3s ease;
}

.otp-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.otp-input.filled {
    border-color: #3b82f6;
    background: #f0f9ff;
}

/* Email Display */
.email-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
    padding: 10px 20px;
    background: #f0f9ff;
    border-radius: 8px;
    color: #1e40af;
    font-weight: 500;
}

/* Resend Section */
.resend-section {
    text-align: center;
    margin: 20px 0;
}

.resend-btn {
    background: none;
    border: none;
    color: #3b82f6;
    cursor: pointer;
    text-decoration: underline;
    font-size: 0.9rem;
}

.resend-btn:hover {
    color: #1e40af;
}

.resend-btn:disabled {
    color: #9ca3af;
    cursor: not-allowed;
    text-decoration: none;
}

.countdown {
    color: #666;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* Password Strength */
.password-strength {
    margin: 15px 0;
}

.strength-meter {
    width: 100%;
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.strength-bar {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 3px;
}

.strength-bar.weak {
    width: 25%;
    background: #ef4444;
}

.strength-bar.fair {
    width: 50%;
    background: #f59e0b;
}

.strength-bar.good {
    width: 75%;
    background: #10b981;
}

.strength-bar.strong {
    width: 100%;
    background: #059669;
}

.strength-text {
    font-size: 0.8rem;
    color: #666;
}

/* Send OTP Button */
.send-otp-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: #3b82f6;
    color: #fff;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    z-index: 2;
}

.send-otp-btn:hover {
    background: #2563eb;
}

.send-otp-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}



/* Forgot Password Steps */
.forgot-step {
    display: none;
}

.forgot-step.active {
    display: block;
}

/* Authentication Messages */
.auth-message {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    animation: slideDown 0.3s ease;
}

.auth-message-success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.auth-message-error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.auth-message-info {
    background: #eff6ff;
    color: #1d4ed8;
    border: 1px solid #bfdbfe;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design for Auth Pages */
@media (max-width: 768px) {
    .auth-container {
        max-width: 90%;
        padding: 0 20px;
    }

    .auth-card {
        padding: 30px 20px;
        max-width: 100%;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .otp-input-group {
        gap: 8px;
    }

    .otp-input {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .auth-section {
        padding: 20px 0;
    }

    .auth-container {
        max-width: 95%;
        padding: 0 10px;
    }

    .auth-card {
        padding: 20px 15px;
        max-width: 100%;
    }

    .auth-header h2 {
        font-size: 1.5rem;
    }

    .otp-input {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .send-otp-btn {
        font-size: 0.7rem;
        padding: 6px 10px;
    }
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: #fff;
    padding: 40px 0 20px;
    /* margin-top: 60px; */
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h4 {
    color: #fff;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: #e2e8f0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #fff;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 20px;
    text-align: center;
    color: #e2e8f0;
}

/* Error Message Styles */
.error-message {
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
    font-weight: 500;
}

.invalid-feedback {
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
    font-weight: 500;
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error,
.form-group input.is-invalid,
.form-group select.is-invalid,
.form-group textarea.is-invalid {
    border-color: #dc2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-group input:focus.error,
.form-group select:focus.error,
.form-group textarea:focus.error,
.form-group input:focus.is-invalid,
.form-group select:focus.is-invalid,
.form-group textarea:focus.is-invalid {
    border-color: #dc2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.2);
}